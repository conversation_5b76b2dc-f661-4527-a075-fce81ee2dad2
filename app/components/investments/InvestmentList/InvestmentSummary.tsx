"use client";

import { Card, CardContent } from "@/components/ui/card";
import { useInvestment } from "@/contexts/investment-context";
import { Activity, DollarSign, TrendingUp, Users } from "lucide-react";
import { Investment, InvestmentStatus } from "@/app/data/investmentModel";

type InvestmentSummaryProps = {
  investments: Investment[];
  tabStatus: string;
};

export const InvestmentSummary = ({
  investments,
  tabStatus,
}: InvestmentSummaryProps) => {
  const { getInitialAmount, circleMembers } = useInvestment();
  const noOfUsers = circleMembers.length;

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);

  // --- Filter based on tabStatus ---
  let filteredInvestments: Investment[] = [];

  if (tabStatus === InvestmentStatus.ACTIVE) {
    filteredInvestments = investments.filter(
      (inv) => inv.status === InvestmentStatus.ACTIVE
    );
  } else if (tabStatus === InvestmentStatus.MATURED) {
    filteredInvestments = investments.filter(
      (inv) => inv.status === InvestmentStatus.MATURED
    );
  } else if (tabStatus === InvestmentStatus.CLOSED) {
    filteredInvestments = investments.filter(
      (inv) =>
        inv.status === InvestmentStatus.CLOSED ||
        inv.status === InvestmentStatus.MATURED
    );
  }

  // --- Totals ---
  const totalInvested = filteredInvestments.reduce((sum, inv) => {
    const { initialAmount } = getInitialAmount(inv);
    return sum + initialAmount;
  }, 0);

  const totalCurrentValue = filteredInvestments.reduce(
    (sum, inv) => sum + (inv.currentAmount || 0),
    0
  );

  // For closed: show amount + profit
  const totalForClosed =
    tabStatus === InvestmentStatus.CLOSED ? totalCurrentValue : totalInvested;

  const totalProfit = totalCurrentValue - totalInvested;

  const activeInvestments = investments.filter(
    (inv) => inv.status === InvestmentStatus.ACTIVE
  ).length;
  const maturedInvestments = investments.filter(
    (inv) => inv.status === InvestmentStatus.MATURED
  ).length;

  const summaryCards = [
    {
      value: formatCurrency(
        tabStatus === InvestmentStatus.CLOSED ? totalForClosed : totalInvested
      ),
      title:
        tabStatus === InvestmentStatus.CLOSED
          ? "Total Value"
          : "Total Invested",
      description:
        tabStatus === InvestmentStatus.CLOSED
          ? "Matured & closed"
          : "Active only",
      Icon: DollarSign,
      iconClass: "text-blue-600 dark:text-blue-400",
      bgClass: "bg-blue-100 dark:bg-blue-900",
    },
    {
      value: formatCurrency(totalProfit),
      // title: "Current Value",
      title: "Profit",
      description: `${formatCurrency(totalInvested)} invested`,
      Icon: TrendingUp,
      iconClass: "text-green-600 dark:text-green-400",
      bgClass: "bg-green-100 dark:bg-green-900",
      trend: true,
    },
    {
      value: activeInvestments,
      title: "Active Investments",
      description: `${maturedInvestments} ready for withdrawal`,
      Icon: Activity,
      iconClass: "text-[#245c1a] dark:text-green-400",
      bgClass: "bg-[#245c1a]/10 dark:bg-green-600/20",
    },
    {
      value: noOfUsers,
      title: "Circle Members",
      description: "People in your circle",
      Icon: Users,
      iconClass: "text-purple-600 dark:text-purple-400",
      bgClass: "bg-purple-100 dark:bg-purple-900",
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6 mb-6 md:mb-8">
      {summaryCards.map(
        (
          { title, value, description, Icon, iconClass, bgClass, trend },
          idx
        ) => (
          <Card key={idx}>
            <CardContent className="p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 truncate">
                    {title}
                  </p>
                  <p
                    className={`text-lg md:text-2xl font-bold ${
                      trend
                        ? "text-green-600 dark:text-green-400"
                        : "text-gray-900 dark:text-white"
                    } truncate`}
                  >
                    {value}
                  </p>
                </div>
                <div
                  className={`p-2 md:p-3 rounded-full flex-shrink-0 ml-2 ${bgClass}`}
                >
                  <Icon className={`w-4 h-4 md:w-6 md:h-6 ${iconClass}`} />
                </div>
              </div>
              {description && (
                <div className="mt-1 md:mt-2">
                  <span className="text-xs md:text-sm text-gray-500 dark:text-gray-400">
                    {description}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        )
      )}
    </div>
  );
};
