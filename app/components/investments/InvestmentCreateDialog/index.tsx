"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useInvestment } from "@/contexts/investment-context";
import { ArrowLeft } from "lucide-react";
import { useRef, useState } from "react";
import { AppDrawerRef, ApplicationDialog } from "../../dialog";
import { InvestmentForm, InvestmentFormType } from "./InvestmentForm";
import { InvestmentPaymentSummary } from "./InvestmentPaymentSummary";
import { useToast } from "@/hooks/use-toast";

type InvestmentFormRefProps = {
  investmentData: () => InvestmentFormType | undefined;
  reset: () => void;
  validate: () => boolean;
};

type InvestmentCreateDialogProps = {
  ref: React.Ref<AppDrawerRef>;
  selectedInvestmentId?: string;
  onClose: () => void;
  onTabChange: (tab: string) => void;
};

export const InvestmentCreateDialog = ({
  ref: appDrawerRef,
  selectedInvestmentId = "",
  onClose,
  onTabChange,
}: InvestmentCreateDialogProps) => {
  const { toast } = useToast();
  const { getInitialAmount, investments, onUpdateInvestment } = useInvestment();

  const [paymentStep, setPaymentStep] = useState<"details" | "payment">(
    selectedInvestmentId ? "payment" : "details"
  );
  const investmentFormRef = useRef<InvestmentFormRefProps>(null);
  const [isDisabled, setIsDisabled] = useState(!Boolean(selectedInvestmentId));
  const [newInvestment, setNewInvestment] = useState<
    InvestmentFormType | undefined
  >(buildInitialInvestment());

  const validateInvestmentForm = () => {
    if (!investmentFormRef.current) {
      throw new Error("investmentFormRef is not set");
    }
    const isValid = investmentFormRef.current.validate();
    if (!isValid) {
      throw new Error("Form has errors!");
    }
    const { investmentData } = investmentFormRef.current;
    const result = investmentData();
    if (!result) {
      throw new Error("Form data is invalid");
    }
    return result;
  };

  const onSubmit = async () => {
    try {
      if (paymentStep === "details") {
        const formData = validateInvestmentForm();
        setNewInvestment(formData);
        setIsDisabled(true);
        await onUpdateInvestment({
          formData,
          investmentId: selectedInvestmentId,
        });
        onTabChange("all");
        setPaymentStep("payment");
        toast({
          title: "Investment Created",
          description:
            "Your investment details have been saved. Proceed to payment.",
        });
      } else {
        setIsDisabled(true);

        await onUpdateInvestment({
          investmentId: selectedInvestmentId,
        });
        onTabChange!("pending");
        onClose();
        toast({
          title: "Processing Deposit",
          description:
            "Your payment is being verified. You’ll receive an update shortly.",
        });
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
      setIsDisabled(false);
    } finally {
      setIsDisabled(false);
    }
  };

  function buildInitialInvestment(): InvestmentFormType | undefined {
    const initialInvestment = investments.find(
      (inv) => inv.id === selectedInvestmentId
    );
    if (!initialInvestment) return undefined;
    const { beneficiaries } = getInitialAmount(initialInvestment);
    return {
      name: initialInvestment.name,
      duration: initialInvestment.duration.toString(),
      beneficiaries: beneficiaries.map((b) => ({
        id: b.userId,
        type: b.type as "self" | "friend",
        beneficiaryId: b.userId,
        beneficiaryName: b.name,
        amount: b.amount.toString(),
        percentage: b.percentage.toString(),
      })),
    };
  }
  return (
    <>
      {/* Create Investment Drawer - Fixed Scrolling for All Screen Sizes */}
      <ApplicationDialog
        isDisabled={isDisabled}
        componentType="drawer"
        ref={appDrawerRef}
        title={
          paymentStep === "details"
            ? "Create New Investment"
            : "Complete Payment"
        }
        description={
          paymentStep === "details"
            ? "Start a new USDT investment with compound interest."
            : "Complete your payment to activate the investment."
        }
        icon="wallet"
        headerButton={
          paymentStep === "payment" && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPaymentStep("details")}
              className="mr-2 p-1 h-8 w-8"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
          )
        }
        submitButtonProps={
          paymentStep === "details"
            ? {
                content: "Proceed to Payment",
              }
            : {
                content: "I've Sent the Payment",
              }
        }
        onSubmit={onSubmit}
      >
        {paymentStep === "details" ? (
          <InvestmentForm
            ref={investmentFormRef}
            onChange={(valid) => setIsDisabled(!valid)}
            initialInvestment={buildInitialInvestment()}
          />
        ) : (
          <InvestmentPaymentSummary newInvestment={newInvestment!} />
        )}
      </ApplicationDialog>
    </>
  );
};
