import NavGuard from "@/components/navbar-guard";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster as SonnerToaster } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { loadTranslations } from "@/contexts/actions";
import { TranslationProvider } from "@/contexts/translation-context";
import { UserProvider } from "@/contexts/user-provider";
import { QueryProvider } from "@/lib/providers/query-provider";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import type React from "react";
import { getLoggedInUser } from "./auth/actions";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "TradeSmart - Beginner-Friendly Trading Platform",
  description:
    "A trading platform designed for users with little to no experience in trading",
  generator: "v0.dev",
};

export const dynamic = 'force-dynamic';

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const user = await getLoggedInUser();
  const translations = await loadTranslations();

  return (
    <>
      <html lang="en" suppressHydrationWarning>
        <body className={inter.className}>
          <QueryProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              enableSystem
              disableTransitionOnChange
            >
              <TranslationProvider translations={translations}>
                <UserProvider user={user}>
                  <>
                    <NavGuard />
                    {children}
                  </>
                </UserProvider>
              </TranslationProvider>
              <Toaster />
              <SonnerToaster />
            </ThemeProvider>
          </QueryProvider>
        </body>
      </html>
    </>
  );
}
