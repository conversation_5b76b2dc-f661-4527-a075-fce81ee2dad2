import { redirect } from "next/navigation";
import { getLoggedInUser } from "../auth/actions";
import { getDBService } from "@/lib/services/db";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getLoggedInUser();

  // Basic auth check
  if (!user) {
    redirect("/auth/login");
  }

  // Handle pending approval cases first
  if (user.approved === false) {
    if (user.admin?.trim()) {
      redirect("/onboarding/pending-approval");
    } else {
      redirect("/onboarding/user-type");
    }
  }

  // Check if user type is missing (only for approved users who are not admins)
  if ((!user.type || user.type.trim() === "") && user.role !== "admin") {
    console.log("Dashboard Layout - Redirecting to user-type: missing type");
    redirect("/onboarding/user-type");
  }

  // Check admin validation only if the current user is NOT already an admin
  if (user.admin && user.role !== "admin") {
    // If user has admin field but no expand.admin data, fetch it
    if (!user.expand?.admin) {
      try {
        const dbService = await getDBService();
        const inviteOwner = await dbService.pb
          .collection("users")
          .getOne(user.admin);

        if (inviteOwner.role !== "admin") {
          redirect("/onboarding/user-type");
        }
      } catch (error) {
        console.error("Error checking invite owner role:", error);
        redirect("/onboarding/user-type");
      }
    } else {
      // Use expand data if available
      if (user.expand.admin.role !== "admin") {
        redirect("/onboarding/user-type");
      }
    }
  }

  return <>{children}</>;
}