"use client";

import { AccountsCard } from "../components/accountCard";
import TotalInvestmentSummary from "../components/investments/DashboardTotalInvestmentSummary";
import { PerformanceSummary } from "../components/performanceSummary";
export const DashboardView = () => {
  const accounts = [
    {
      id: "1",
      name: "Primary Binance Account",
      exchange: "binance",
      currentValue: 12500.75,
      initialInvestment: 10000,
      daysElapsed: 120,
      totalDays: 180, // ✅ new
      totalProfit: 2500.75,
      startDate: "2025-01-01", // ✅ new
      status: "active",
    },
    {
      id: "2",
      name: "Coinbase Savings",
      exchange: "coinbase",
      currentValue: 8300.5,
      initialInvestment: 9000,
      daysElapsed: 90,
      totalDays: 150, // ✅ new
      totalProfit: -699.5,
      startDate: "2025-02-15", // ✅ new
      status: "inactive",
    },
    {
      id: "3",
      name: "Kraken Futures",
      exchange: "kraken",
      currentValue: 4200.0,
      initialInvestment: 3500,
      daysElapsed: 45,
      totalDays: 90, // ✅ new
      totalProfit: 700,
      startDate: "2025-03-10", // ✅ new
      status: "active",
    },
  ];

  const mainAccount = accounts[0];

  return (
    <>
      <div className="flex flex-col space-y-4 mb-6 md:mb-8 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
            Investment Manager
          </h1>
          <p className="text-sm md:text-base text-gray-600 dark:text-gray-300">
            Manage your USDT investments and track compound growth
          </p>
        </div>
      </div>
      <div className="space-y-6">
        <TotalInvestmentSummary
          summary={{
            startingValue: mainAccount.initialInvestment,
            currentValue: mainAccount.currentValue,
            daysElapsed: mainAccount.daysElapsed,
            totalDays: mainAccount.totalDays,
            totalProfit: mainAccount.totalProfit,
            startDate: mainAccount.startDate,
          }}
        />

        {/* Account Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-start">
          <AccountsCard
            accounts={accounts}
            onViewDetails={(id) => console.log("View details clicked:", id)}
            onSettingsClick={(id) => console.log("Settings clicked:", id)}
            onAddAccount={() => console.log("Add new account clicked")}
          />

          <PerformanceSummary
            totalReturn={
              (mainAccount.currentValue / mainAccount.initialInvestment - 1) *
              100
            }
            daysActive={mainAccount.daysElapsed}
            avgDailyProfit={
              mainAccount.totalProfit / Math.max(mainAccount.daysElapsed, 1)
            }
          />
        </div>
      </div>
    </>
  );
};
