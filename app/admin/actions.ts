"use server";
import { accountModel } from "@/app/data/accountModel";
import { WalletType } from "@/contexts/admin-provider";
import { DBService, getDBService, getIDBService } from "@/lib/services/db";
import {
  deleteMultipleCookies,
  getMultipleCookies,
  setMultipleCookies,
} from "@/lib/utils/cookie";
import { validateWalletAddress } from "@/lib/utils/formValidation";
import { getLoggedInUser } from "../auth/actions";
import { InvestmentFormType } from "../components/investments/InvestmentCreateDialog/InvestmentForm";
import { Investment, InvestmentStatus } from "../data/investmentModel";

export async function getUser(userId: string) {
  try {
    const dbService = await getIDBService();
    const user = await dbService.getUserById(userId);
    return user;
  } catch (error) {
    return null;
  }
}

export async function fetchInvestments() {
  return accountModel.mockAccounts();
}

export async function createOrUpdateInvestment(
  investmentData: InvestmentFormType,
  existingInvestmentId?: string
) {
  const user = await getLoggedInUser();
  const dbService = await getDBService();

  let result;

  if (!existingInvestmentId) {
    // create new investment with beneficiaries
    result = await dbService.createInvestment(user?.id!, investmentData);
  } else {
    // update existing investment
    result = await dbService.updateInvestment(
      existingInvestmentId,
      investmentData
    );
  }

  // Manually merge beneficiaries from the form data
  const beneficiaries = investmentData.beneficiaries || [];
  const circleMembers = beneficiaries
    .filter((b) => b.beneficiaryId !== user?.id)
    .map((b) => b.beneficiaryId);
  const multiSetupData = beneficiaries.map((b) => ({
    userId: b.beneficiaryId,
    amount: b.amount,
    percentage: b.percentage,
  }));

  const mergedInvestment = {
    id: existingInvestmentId || result.id,
    name: result.name,
    duration: result.duration,
    interestRate: result.interest_rate,
    status: result.status,
    createdAt: result.createdAt,
    // currentAmount: result.initial_amount + (result.total_profit || 0),
    circleMembers,
    multiSetupData,
  };

  return mergedInvestment as unknown as Investment;
}

export async function getInvestments() {
  const user = await getLoggedInUser();
  const dbService = await getDBService();
  const investments = await dbService.getMergedInvestments(user?.id!);
  return investments;
}

export async function withdrawalAddressChangeVerification() {
  try {
    const user = await getLoggedInUser();
    if (!user?.email) {
      return {
        success: false,
        error: "User is not logged in or email not found",
      };
    }

    const dbService = await getDBService();
    const result = await dbService.requestOtp(user.email);

    // Store only OTP-related metadata
    const cookiesToSet = [
      { key: "otpId", value: result.otpId },
      { key: "otpTimestamp", value: Date.now().toString() },
    ];

    await setMultipleCookies(cookiesToSet);

    return {
      success: true,
      message: "OTP sent successfully to your email",
    };
  } catch (error) {
    console.error("Withdrawal OTP failed:", error);
    return {
      success: false,
      error: true,
    };
  }
}

export async function verifyOTP(otp: string) {
  const sessionData = await getMultipleCookies(["otpId"]);
  const otpId = sessionData.otpId;

  if (!otpId) {
    return {
      success: false,
      error:
        "No OTP verification session found. Please request a new verification code first",
    };
  }

  if (!otp) {
    return {
      success: false,
      error: "OTP code is required",
    };
  }

  try {
    const user = await getLoggedInUser();
    if (!user?.email) {
      return {
        success: false,
        error: "User not logged in or email not found",
      };
    }

    const dbService = await getDBService();
    const validation = await dbService.validateOtp({
      email: user.email,
      otpId,
      otp,
    });

    if (validation?.success) {
      // Clear only OTP cookies, keep USER_KEY intact
      await deleteMultipleCookies(["otpId", "otpTimestamp"]);

      return {
        success: true,
        message: "OTP verified successfully",
        data: validation,
      };
    } else {
      return {
        success: false,
        error: "OTP verification failed - mismatch",
      };
    }
  } catch (error) {
    console.error("OTP verification failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to verify OTP",
    };
  }
}

function validateInputs(wallet: WalletType, code: string) {
  if (!wallet.address || !wallet.network) {
    return {
      field: "walletAddress",
      error: "Wallet address and network are required.",
    };
  }

  if (!code) {
    return {
      field: "verificationCode",
      error: "Verification code is required.",
    };
  }

  const validationError = validateWalletAddress(wallet.address, wallet.network);
  if (validationError) {
    return { field: "walletAddress", error: validationError };
  }

  return null;
}

export type UpdateWithdrawalResponse =
  | {
      success: true;
      data: WalletType;
    }
  | {
      success: false;
      error: string;
      field?: string;
    };

export async function validateWalletFields(wallet: WalletType, code: string) {
  const validation = validateInputs(wallet, code);
  if (validation) {
    return { success: false, ...validation };
  }
  const dbService = await getDBService();
  const verification = await verifyOTP(code);
  if (!verification.success) {
    return {
      success: false,
      field: "verificationCode",
      error: verification.error || "Invalid or expired verification code.",
    };
  }
  const isBlacklisted = await dbService.checkIsWalletExist({
    ...wallet,
    blacklisted: true,
  });
  if (isBlacklisted) {
    return {
      success: false,
      field: "walletAddress",
      error: "This wallet address is blocked. Contact support.",
    };
  }
  const walletExists = await dbService.checkIsWalletExist(wallet);
  if (walletExists) {
    return {
      success: false,
      field: "walletAddress",
      error: "This wallet address already exists, please use another one.",
    };
  }

  return { success: true };
}

export async function updateWithdrawalAddress(
  wallet: WalletType
): Promise<UpdateWithdrawalResponse> {
  const user = await getLoggedInUser();
  const dbService = await getDBService();

  // Create new wallet
  try {
    const newWallet = await dbService.createWallet(user?.id!, wallet);
    return {
      success: true,
      data: {
        id: newWallet.id,
        address: newWallet.address,
        network: newWallet.network,
      },
    };
  } catch (error: any) {
    console.log("Failed to create wallet:", error);

    const errorMessage =
      error?.response?.data?.address?.code === "validation_not_unique"
        ? "This wallet address is already in use."
        : error?.response?.data?.address?.message;

    return {
      success: false,
      field: "walletAddress",
      error:
        errorMessage ||
        "Something went wrong while creating your wallet. Please try again later.",
    };
  }
}
export async function processWithdrawalPayment(
  investment: Investment,
  wallet?: WalletType
) {
  try {
    const paymentType = wallet ? "withdrawal" : "payment";
    const walletId = wallet?.id ?? process.env.DEPOSIT_WALLET_ID;

    if (!walletId) {
      throw new Error("No wallet ID provided or configured.");
    }

    const dbService = await getDBService();
    await dbService.createWithdrawalRequest(investment.id, walletId);

    return {
      success: true,
      message: `Your ${paymentType} request has been submitted successfully.`,
    };
  } catch (error) {
    console.error("Withdrawal/Payment request failed:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Withdrawal/Payment request failed",
    };
  }
}

export async function validateAndSaveAddress(wallet: WalletType, code: string) {
  const validationResult = await validateWalletFields(wallet, code);
  if (!validationResult.success) {
    return validationResult;
  }

  const result = await updateWithdrawalAddress(wallet);
  // needed the result to return the wallet id for the investment to be updated

  if (result.success) {
    return {
      success: true,
      data: result.data,
    };
  } else {
    return {
      success: false,
      error: result.error,
      field: result.field,
    };
  }
}



//create a function that delete a withdrawal_request

