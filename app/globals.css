@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

/* Enhanced brand color adjustments for dark mode */
.dark .text-\[\#245c1a\] {
  color: rgb(74 222 128) !important; /* green-400 - brighter for dark mode */
}

.dark .bg-\[\#245c1a\] {
  background-color: rgb(34 197 94) !important; /* green-500 */
  color: rgb(255 255 255) !important; /* white text for contrast */
}

.dark .hover\:bg-\[\#1a4513\]:hover {
  background-color: rgb(21 128 61) !important; /* green-700 */
  color: rgb(255 255 255) !important; /* white text */
}

.dark .border-\[\#245c1a\] {
  border-color: rgb(74 222 128) !important; /* green-400 */
}

.dark .bg-\[\#e6f0e4\] {
  background-color: rgb(20 83 45) !important; /* dark green equivalent */
  color: rgb(255 255 255) !important; /* white text */
}

.dark .bg-\[\#f8faf7\] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Ensure proper contrast for all text elements */
.dark .text-black {
  color: rgb(255 255 255) !important; /* white text in dark mode */
}

.dark .text-gray-900 {
  color: rgb(255 255 255) !important; /* white text in dark mode */
}

.dark .text-gray-800 {
  color: rgb(229 231 235) !important; /* gray-200 */
}

.dark .text-gray-700 {
  color: rgb(209 213 219) !important; /* gray-300 */
}

.dark .text-gray-600 {
  color: rgb(156 163 175) !important; /* gray-400 */
}

.dark .text-gray-500 {
  color: rgb(156 163 175) !important; /* gray-400 */
}

.dark .text-gray-400 {
  color: rgb(156 163 175) !important; /* gray-400 */
}

/* Background colors for dark mode */
.dark .bg-white {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

.dark .bg-gray-50 {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
}

.dark .bg-gray-100 {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.dark .hover\:bg-gray-50:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.dark .hover\:bg-gray-100:hover {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Border colors for dark mode */
.dark .border-gray-200 {
  border-color: hsl(var(--border)) !important;
}

.dark .border-gray-300 {
  border-color: hsl(var(--border)) !important;
}

.dark .divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: hsl(var(--border)) !important;
}

/* Input and form elements */
.dark input {
  background-color: hsl(var(--input)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dark input::placeholder {
  color: hsl(var(--muted-foreground)) !important;
}

.dark textarea {
  background-color: hsl(var(--input)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dark textarea::placeholder {
  color: hsl(var(--muted-foreground)) !important;
}

.dark select {
  background-color: hsl(var(--input)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Button enhancements for dark mode */
.dark .bg-blue-600 {
  background-color: rgb(37 99 235) !important; /* blue-600 */
  color: rgb(255 255 255) !important;
}

.dark .hover\:bg-blue-700:hover {
  background-color: rgb(29 78 216) !important; /* blue-700 */
  color: rgb(255 255 255) !important;
}

.dark .bg-red-600 {
  background-color: rgb(220 38 38) !important; /* red-600 */
  color: rgb(255 255 255) !important;
}

.dark .hover\:bg-red-700:hover {
  background-color: rgb(185 28 28) !important; /* red-700 */
  color: rgb(255 255 255) !important;
}

/* Success and error states */
.dark .text-green-600 {
  color: rgb(74 222 128) !important; /* green-400 */
}

.dark .text-red-600 {
  color: rgb(248 113 113) !important; /* red-400 */
}

.dark .bg-green-50 {
  background-color: rgb(20 83 45) !important; /* dark green */
  color: rgb(255 255 255) !important;
}

.dark .bg-red-50 {
  background-color: rgb(127 29 29) !important; /* dark red */
  color: rgb(255 255 255) !important;
}

/* Navigation and header elements */
.dark nav {
  background-color: hsl(var(--card)) !important;
  border-color: hsl(var(--border)) !important;
}

.dark header {
  background-color: hsl(var(--card)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Card and container elements */
.dark .shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(255, 255, 255, 0.05) !important;
}

.dark .shadow {
  box-shadow: 0 1px 3px 0 rgba(255, 255, 255, 0.1), 0 1px 2px 0 rgba(255, 255, 255, 0.06) !important;
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(255, 255, 255, 0.1), 0 4px 6px -2px rgba(255, 255, 255, 0.05) !important;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure proper text contrast in all scenarios */
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
  color: rgb(255 255 255) !important;
}

.dark p {
  color: rgb(229 231 235) !important; /* gray-200 */
}

.dark span {
  color: inherit;
}

.dark strong,
.dark b {
  color: rgb(255 255 255) !important;
}

/* Link colors */
.dark a {
  color: rgb(74 222 128) !important; /* green-400 */
}

.dark a:hover {
  color: rgb(34 197 94) !important; /* green-500 */
}

/* Table elements */
.dark table {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

.dark th {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dark td {
  border-color: hsl(var(--border)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Modal and dialog elements */
.dark .fixed {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Ensure icons maintain proper visibility */
.dark svg {
  color: inherit;
}

/* Progress bars and indicators */
.dark .bg-blue-500 {
  background-color: rgb(59 130 246) !important; /* blue-500 */
}

.dark .bg-green-500 {
  background-color: rgb(34 197 94) !important; /* green-500 */
}

.dark .bg-yellow-500 {
  background-color: rgb(234 179 8) !important; /* yellow-500 */
}

.dark .bg-purple-500 {
  background-color: rgb(168 85 247) !important; /* purple-500 */
}
