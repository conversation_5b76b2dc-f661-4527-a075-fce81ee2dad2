export const InvestmentStatus = {
  ACTIVE: "active",
  MATURED: "matured",
  CLOSED: "closed",
  INITIALIZE: "initialize",
} as const;

// Type of the values of InvestmentStatus
export type InvestmentStatusType =
  (typeof InvestmentStatus)[keyof typeof InvestmentStatus];


// models/investmentModel.ts
export interface Investment {
  id: string;
  name: string;
  currentAmount?: number;
  duration: number; // in months
  interestRate?: number; // monthly rate (10% for creator, custom for beneficiary)
  startDate?: string;
  status: InvestmentStatusType;
  createdAt?: string;
  updateAt?: string;
  verifyingPayment?: boolean;
  paymentsCount?: number;
  withdrawn_at?: string;
  withdrawal_request?: string;
  // Circle-related fields
  circleMembers: string[];
  // Add multi-setup data
  multiSetupData: {
    userId: string;
    amount: number;
    percentage: number;
  }[];
}
