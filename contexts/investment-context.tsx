"use client";

import {
  createOrUpdateInvestment,
  processWithdrawalPayment,
} from "@/app/admin/actions";
import { Investment } from "@/app/data/investmentModel";
import { createContext, useContext, useState } from "react";
import { useAdmin } from "./admin-provider";

interface InvestmentContextType {
  investments: Investment[];
  setInvestments: React.Dispatch<React.SetStateAction<Investment[]>>;
}

const InvestmentContext = createContext<InvestmentContextType | undefined>(
  undefined
);

interface InvestmentProviderProps {
  children: React.ReactNode;
  investments: Investment[];
}

export function InvestmentProvider({
  children,
  investments: mockInvestments,
}: InvestmentProviderProps) {
  const [investments, setInvestments] = useState<Investment[]>(mockInvestments);

  const value = { investments, setInvestments };

  return (
    <InvestmentContext.Provider value={value}>
      {children}
    </InvestmentContext.Provider>
  );
}

export type UseInvestmentType = ReturnType<typeof useInvestment>;

export function useInvestment() {
  const {
    circleMembers,
    deleteCircleMember,
    onRemoveCircleMember,
    user,
    wallet,
  } = useAdmin();
  const context = useContext(InvestmentContext);

  if (context === undefined) {
    throw new Error("useInvestment must be used within an InvestmentProvider");
  }
  const { investments, setInvestments } = context;

  const [_investmentId, setInvestmentId] = useState<string | undefined>("");
  const getInitialAmount = (investment: Investment) => {
    const adminUserInfo: {
      id: string;
      name: string;
      email: string;
      addedAt: string;
    } = {
      id: user?.id!,
      name: user?.name!,
      email: user?.email!,
      addedAt: user?.created!,
    };
    const fullCircleMembers = investment.circleMembers
      .map((o) => {
        const member = circleMembers.find((m) => m.id === o);
        return member;
      })
      .filter(Boolean);

    const beneficiaries = investment.multiSetupData.map((o) => {
      const member = fullCircleMembers.find((m) => m?.id === o.userId);
      const foundMember = member || adminUserInfo;
      return {
        ...foundMember,
        ...o,
        type: o.userId === adminUserInfo.id ? "self" : "friend",
        name: foundMember.name || foundMember.email,
      };
    });

    const initialAmount = beneficiaries.reduce(
      (total, beneficiary) =>
        total + parseFloat(String(beneficiary.amount || "0")),
      0
    );

    return {
      initialAmount,
      adminUser: adminUserInfo,
      beneficiaries,
    };
  };

  const transformedInvestment = (investment: Investment) => {
    const canWithdraw = investment.status === "matured";
    const isForSelf = investment.multiSetupData
      .map((o) => o.userId)
      .includes(user?.id!);

    const { adminUser, initialAmount, beneficiaries } =
      getInitialAmount(investment);

    const startDate = investment.startDate
      ? new Date(investment.startDate)
      : null;
    const duration = investment.duration; // in months
    const endDate = startDate ? new Date(startDate) : null; // clone startDate
    let monthsElapsed = 0;
    if (endDate && startDate) {
      endDate.setMonth(endDate.getMonth() + duration);
      const currentDate = new Date();
      monthsElapsed =
        (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
        (currentDate.getMonth() - startDate.getMonth());
      if (currentDate.getDate() < startDate.getDate()) {
        monthsElapsed--;
      }
    }

    const beneficiaryRate = 10;
    const network = wallet?.network || "TRC20";
    const walletAddress = wallet?.address || "";
    const paymentMethod = "crypto";

    const multiSetupData = beneficiaries.map((b) => {
      const ratio = b.amount / initialAmount;
      return {
        ...b,
        beneficiaryName:
          circleMembers.find((m) => m.id === b.userId)?.name || b.name,
        beneficiaryId: b.userId,
        type: b.userId === adminUser.id ? "self" : "friend",
        currentAmount: ratio * (investment.currentAmount || initialAmount),
      };
    });

    // const progressPercentage = (monthsElapsed / duration) * 100;

    const targetAmount =
      initialAmount * (1 + (investment.interestRate || 0) / 100) ** duration;

    const profitSoFar =
      (investment.currentAmount || initialAmount) - initialAmount;
    const totalProfit = targetAmount - initialAmount;

    let progressPercentage = 0;
    if (totalProfit > 0) {
      progressPercentage = (profitSoFar / totalProfit) * 100;
    }

    const verifyingPayment = investment.verifyingPayment || false;

    return {
      currentAmount: investment.currentAmount || initialAmount,
      status: investment.status,
      canWithdraw,
      id: investment.id,
      name: investment.name,
      isForSelf,
      // beneficiaryName: beneficiaries[0].name,
      // createdByName: beneficiaries[0].name,
      beneficiaryName: beneficiaries.map((b) => b.name),
      createdByName: adminUser?.name || "Unknown",

      startDate: investment.startDate,
      endDate: endDate ? endDate.toISOString().split("T")[0] : "n/a",
      initialAmount,
      beneficiaryRate,
      monthsElapsed,
      network,
      walletAddress,
      duration,
      paymentMethod,
      multiSetupData,
      progressPercentage,
      verifyingPayment,
      profitPaymentsCount: investment.paymentsCount || 0,
      withdrawn_at: investment.withdrawn_at,
    };
  };

  const onUpdateInvestment = (investment: Investment) => {
    const existing = investments.find((inv) => inv.id === investment.id);
    if (existing) {
      setInvestments((prev) =>
        prev.map((inv) => (inv.id === investment.id ? investment : inv))
      );
    } else {
      setInvestments((prev) => [investment, ...prev]);
    }
  };

  const onSubmit = async (payload?: {
    formData?: any;
    investmentId?: string;
  }) => {
    const { formData, investmentId } = payload || {};
    if (formData) {
      const newInvestment = await createOrUpdateInvestment(
        formData,
        investmentId
      );
      onUpdateInvestment(newInvestment);
      setInvestmentId(newInvestment.id);
    } else {
      //Processing a deposit
      const investment_Id = investmentId ? investmentId : _investmentId;
      const investment = investments.find((inv) => inv.id === investment_Id);
      if (investment) {
        try {
          const result = await processWithdrawalPayment(
            investment
          );
          if (investment_Id && result.success) {
            updateInvestment(investment_Id, { verifyingPayment: true });
          }
        } catch (error) {
          console.error(error);
        }
      }
    }
  };

  const updateInvestment = (id: string, updates: Partial<Investment>) => {
    const updatedInvestments = investments.map((inv) =>
      inv.id === id ? { ...inv, ...updates } : inv
    );
    setInvestments(updatedInvestments);
  };

  const hasAnyInvestment = (id: string, status?: "active" | "closed") => {
    const relevantInvestments = investments.filter((inv) => {
      if (!status) {
        return true;
      }
      return inv.status === status;
    });
    return relevantInvestments.some((inv) => inv.circleMembers.includes(id));
  };

  const removeOrDeleteMember = async (id: string) => {
    const hasClosed = hasAnyInvestment(id, "closed");
    const noInvestment = !hasAnyInvestment(id);

    if (hasClosed) {
      await onRemoveCircleMember(id);
      return;
    }
    if (noInvestment) {
      await deleteCircleMember(id);
      return;
    }
    throw {
      title: "Cannot Remove",
      description:
        "User has an active investment. Please close the investment before removing the user.",
    };
  };

  return {
    investments,
    onUpdateInvestment: onSubmit,
    transformedInvestment,
    getInitialAmount,
    circleMembers,
    updateInvestment,
    wallet,
    removeOrDeleteMember,
  };
}
