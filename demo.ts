import {
  activateOrCloseInvestment,
  activatePendingInvestments,
  getUser,
} from "./app/admin/actions";
import { loginOrSignup } from "./app/auth/actions";
import { InvestmentStatus } from "./app/data/investmentModel";
import { loadModule, loadTranslations } from "./contexts/actions";
import { getDBService } from "./lib/services/db";

const dbService = await getDBService();

// const restult = await dbService.getUserByEmail("<EMAIL>", true);
// console.log("restult", restult);

// const result = await dbService.getUserById("zzhpka7kzjmye7i")
// console.log(result)
// const result = await dbService.requestOtp("<EMAIL>");
// console.log(result);

// const data = {
//     "email": "<EMAIL>",
//     // "emailVisibility": true,
//     // "name": "test",
//     "role": "user",
//     // "admin": "",
//     // "invite_code": "test",
//     // "approved": true,
//     // "type": "beginner",
//     "password": "12345678",
//     "passwordConfirm": "12345678"
// };

// const record = await dbService.pb.collection('users').create(data);
// console.log("record",record)

// const result = await loadTranslations();
// console.log("result", result);

// const result = await  dbService.getUserById("zzhpka7kzjmye7i");
// console.log("result", result);

// const result = await dbService.getInvestmentBeneficiaries("zzhpka7kzjmye7i");
// console.log("result", result);

// const result = await dbService.getInvestments("zzhpka7kzjmye7i")
// console.log("result", result);

// const result = await dbService.getMergedInvestments("zzhpka7kzjmye7i");
// console.log("result", result);

// const result = await dbService.createWithdrawalRequest("2ayqolkqrhldw0c", "txk9ty5ciz0a8ia");
// console.log("result", result);

// const result = await dbService.createWallet("zzhpka7kzjmye7i", {
//   address: "0x1234567890",
//   network: "ERC20",
// });
// console.log("result", result);

// const data =  {
//   beneficiaries: [
//     {
//       id: "multi_1758884238265",
//       type: "friend",
//       beneficiaryId: "kw3eamlikxqw8ll",
//       beneficiaryName: "Tayo Oyeniyi",
//       amount: "599990",
//       percentage: "5",
//     },
//     {
//       id: "multi_1758884257685",
//       type: "self",
//       beneficiaryId: "ozdadpinszk7gyw",
//       beneficiaryName: "You",
//       amount: "500",
//       percentage: "10",
//     },
//   ],
//   name: "sTayo group",
//   duration: "6",
// };

// const result = await dbService.activateInvestment("u6f9unn6p768ecz")
// console.log(result)

// const result = await dbService.closeInvestment("o4bmtq9qp0msupn")
// console.log(result)

//admin token
// const result = dbService.pb.authStore.token;
// console.log("token",result)

// // send otp to email
// const email = "<EMAIL>"
// // const requestOtp = await dbService.requestOtp(email)
// // console.log("request", requestOtp)

// const requestOtp = "uz0hwq73k0gviem"

// const verify = await dbService.validateOtp({
//     email:email,
//     otpId:requestOtp,
//     otp:"92705606"
// })

// console.log("verify", verify)

// // 🔹 User Management
// console.log("== User Management ==");

const myEmail = "<EMAIL>";
const name = "Tayo";

// const userId = "zzhpka7kzjmye7i"

// // create user
// const newUser = await dbService.createUserRecord({ email:myEmail, name });
// console.log("createUserRecord:", newUser);

// get by email
// const byEmail = await dbService.getUserByEmail(myEmail);
// console.log("getUserByEmail:", byEmail);

// // get by ID
// const byId = await dbService.getUserById(userId);
// console.log("getUserById:", byId);

// // update user
// const updatedUser = await dbService.updateUserRecord({ userId, updates: { name: "Updated Demo User" } });
// console.log("updateUserRecord:", updatedUser);

// // approve
// const approved = await dbService.approveUser(newUser.id);
// console.log("approveUser:", approved);

// // reject
// const rejected = await dbService.rejectUser(newUser.id);
// console.log("rejectUser:", rejected);

// // followers
// const followers = await dbService.getAdminFollowers(userId);
// console.log("getAdminFollowers:", followers);

// // invite code owner
// const inviteOwner = await dbService.getInviteCodeOwner("SomeInviteCode");
// console.log("getInviteCodeOwner:", inviteOwner);

// // delete user
// await dbService.deleteUser(newUser.id);
// console.log("deleteUser: deleted");

// // 🔹 OTP / Auth
// console.log("== OTP / Auth ==");

// const email = "<EMAIL>";
// const otpReq = await dbService.requestOtp(email);
// console.log("requestOtp:", otpReq);

// if (otpReq && "otpId" in otpReq) {
//   // fake OTP usage
//   const otpRes = await dbService.validateOtp({ email, otpId: otpReq.otpId, otp: "123456" });
//   console.log("validateOtp:", otpRes);
// }

// const token = dbService.pb.authStore.token;
// const maxAge = await dbService.determineMaxAge(token);
// console.log("determineMaxAge:", maxAge);

// const tokenDetails = await dbService.validateAndDecodeToken(token);
// console.log("validateAndDecodeToken:", tokenDetails);

// const userFromToken = await dbService.getUserFromToken(token);
// console.log("getUserFromToken:", userFromToken);

// const superUserFromToken = await dbService.getSuperUserFromToken(token);
// console.log("getSuperUserFromToken:", superUserFromToken);

// // 🔹 Wallet
// console.log("== Wallet ==");

// const wallet = await dbService.createWallet(newUser.id, { address: "0xTEST123", network: "ERC20" });
// console.log("createWallet:", wallet);

// const walletExists = await dbService.checkIsWalletExist({ address: "0xTEST123", network: "ERC20" });
// console.log("checkIsWalletExist:", walletExists);

// // 🔹 Investments
// console.log("== Investments ==");

// const inv = await dbService.createInvestment(newUser.id, {
//   name: "Demo Investment",
//   duration: 6,
//   beneficiaries: [
//     { beneficiaryId: newUser.id, amount: 1000, percentage: 50 },
//   ],
// });
// console.log("createInvestment:", inv);

// const invUpdated = await dbService.updateInvestment(inv.id, {
//   name: "Updated Investment",
//   duration: 12,
//   beneficiaries: [],
// });
// console.log("updateInvestment:", invUpdated);

// const invs = await dbService.getInvestments(newUser.id);
// console.log("getInvestments:", invs);

// const invBeneficiaries = await dbService.getInvestmentBeneficiaries(newUser.id);
// console.log("getInvestmentBeneficiaries:", invBeneficiaries);

// const merged = await dbService.getMergedInvestments(newUser.id);
// console.log("getMergedInvestments:", merged);

// const withdrawalReq = await dbService.createWithdrawalRequest(inv.id, wallet.id);
// console.log("createWithdrawalRequest:", withdrawalReq);

// const activated = await dbService.activateInvestment(inv.id);
// console.log("activateInvestment:", activated);

// const closed = await dbService.closeInvestment(inv.id);
// console.log("closeInvestment:", closed);

// // handleBeneficiaries test
// await dbService.handleBeneficiaries(inv.id, [
//   { beneficiaryId: newUser.id, amount: 500, percentage: 100 },
// ]);
// console.log("handleBeneficiaries: executed");

const nonAdminToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2xsZWN0aW9uSWQiOiJfcGJfdXNlcnNfYXV0aF8iLCJleHAiOjE3NTk5MTYzNDcsImlkIjoieHM5dnp3eGU1YzA0dmJ4IiwicmVmcmVzaGFibGUiOnRydWUsInR5cGUiOiJhdXRoIn0.CnUd0tkClH011-zV9JB3I2qRs0UZeIbWKjGQJEawd8A";

//admin token
// const adminToken = dbService.pb.authStore.token;
// console.log("token",adminToken)

// const result = await dbService.getSuperUserFromToken(nonAdminToken);
// const result = await dbService.getUserFromToken(nonAdminToken);
// console.log("result", result)

// const result = await dbService.getInviteCodeOwner("OnelPieceMember")
// console.log("result", result)

// const result = await dbService.determineMaxAge("nonAdminToken")
// console.log("result", result)

// const user = await getUser("zzhpka7klzjmye7i");
// console.log(user)

// export async function activateOrCloseInvestment(investmentId: string, action: string) {
//   const dbService = await getDBService();
//   if (action === "activate") {
//     const updated = await dbService.activateInvestment(investmentId);
//     if (!updated) {
//       return {
//         error:
//           "Cannot activate investment. Either not in 'initialize' state or no withdrawal_request exists.",
//       };
//     }
//     return {
//       message: "Investment activated successfully",
//       investment: updated,
//     };
//   }

//   if (action === "close") {
//     const updated = await dbService.closeInvestment(investmentId);
//     if (!updated) {
//       return {
//         error:
//           "Cannot close investment. Either not in 'matured' state or no withdrawal_request exists.",
//       };
//     }
//     return {
//       message: "Investment closed successfully",
//       investment: updated,
//     };
//   }
// }

// const result = await activateOrCloseInvestment({
//   token: adminToken,
//   investmentId: "u6f9unn6p768ecz",
//   action: "activate",
// });
// console.log("result", result)

// const result = await dbService.getInvestmentsForActivation()
// console.log("result", result[1].expand.investment)

// const result = await activatePendingInvestments()
// console.log("result", result)

// const formData = {
//     email: "<EMAIL>",
//     name: "Tayo",
// }
// const result = await loginOrSignup(formData.email)
// console.log("result", result)

// const result = await dbService.getInvestmentsWithWithdrawalRequest(InvestmentStatus.INITIALIZE)
// console.log("result", result)

const result = await dbService.triggerSuperUserActions({
  investmentId: "u6f9unn6p768ecz",
  action: "reject",
});
console.log("result", result);
