import {
  Investment,
  InvestmentStatus,
  InvestmentStatusType,
} from "@/app/data/investmentModel";
import { WalletType } from "@/contexts/admin-provider";
import PocketBase from "pocketbase";
import { determineMaxAge, validateAndDecodeToken } from "./helpers";

type AuthResultProps = {
  record: {
    admin: string;
    approved: boolean;
    avatar: string;
    collectionId: string;
    collectionName: string;
    created: string;
    email: string;
    emailVisibility: boolean;
    id: string;
    invite_code: string;
    name: string;
    role: string;
    updated: string;
    verified: boolean;
  };
  token: string;
};

type VerifyEmailResponse =
  | {
      expiringTime: number;
      authToken: string;
      success: true;
    }
  | {
      otpId: string;
    }
  | null;

export class DBService {
  public pb: PocketBase;

  constructor(pb: PocketBase) {
    this.pb = pb;
  }

  /** 🧠 Ensure PocketBase admin is still authenticated */
  public async ensureAdminAuth() {
    const email = process.env.NEW_POCKETBASE_EMAIL!;
    const password = process.env.NEW_POCKETBASE_PASSWORD!;

    if (!this.pb.authStore.isValid) {
      console.log("🔑 Re-authenticating PocketBase admin...");
      await this.pb.admins.authWithPassword(email, password);
    }
  }

  // Authentication and Token handling
  async requestOtp(email: string) {
    await this.ensureAdminAuth();
    const result: VerifyEmailResponse = await this.pb
      .collection("users")
      .requestOTP(email);
    return result;
  }

  async validateOtp({
    email,
    otpId,
    otp,
  }: {
    email: string;
    otpId: string;
    otp: string;
  }) {
    await this.ensureAdminAuth();
    const result: AuthResultProps = await this.pb
      .collection("users")
      .authWithOTP(otpId, otp);

    if (result.record.email === email) {
      const maxAge = determineMaxAge(result.token);
      if (maxAge === null) return null;

      return {
        expiringTime: maxAge,
        authToken: result.token,
        success: true,
      };
    }
    return null;
  }

  // User management
  async getUserFromToken(token: string): Promise<UserProps | null> {
    await this.ensureAdminAuth();
    const tokenDetails = validateAndDecodeToken(token);
    if (!tokenDetails) return null;

    const userRecords = await this.pb.collection("users").getFullList({
      filter: `id = "${tokenDetails.id}"`,
      expand: "admin,wallet",
    });

    const userRecord = userRecords[0];
    if (
      userRecord &&
      userRecord.settings &&
      typeof userRecord.settings === "string"
    ) {
      try {
        userRecord.settings = JSON.parse(userRecord.settings);
      } catch {
        userRecord.settings = {};
      }
    }

    return (userRecord || null) as UserProps | null;
  }

  async getSuperUserFromToken(token: string) {
    await this.ensureAdminAuth();
    const tokenDetails = validateAndDecodeToken(token);
    if (!tokenDetails) return null;

    const superUsers = await this.pb.collection("_superusers").getFullList({
      filter: `id = "${tokenDetails.id}"`,
    });
    return superUsers[0] || null;
  }

  async getInviteCodeOwner(inviteCode: string) {
    await this.ensureAdminAuth();
    const userRecord = await this.pb.collection("users").getFullList({
      filter: `invite_code = "${inviteCode}" && role="admin"`,
    });
    return (userRecord[0] || null) as UserProps | null;
  }

  async updateUserRecord({
    userId,
    updates,
  }: {
    userId: string;
    updates: Partial<UserProps>;
  }) {
    await this.ensureAdminAuth();
    return (await this.pb.collection("users").update(
      userId,
      updates
    )) as UserProps;
  }

  async getUserByEmail(email: string, create?: boolean) {
    await this.ensureAdminAuth();
    const lowerEmail = email.toLowerCase().trim();
    const userRecord = await this.pb.collection("users").getFullList({
      filter: `email ~ "${lowerEmail}"`,
    });
    const result = (userRecord[0] || null) as UserProps | null;

    if (!result && create) {
      await this.createUserRecord({ email: lowerEmail });
    }

    return result;
  }

  async createUserRecord(updates: Partial<UserProps> & { email: string }) {
    await this.ensureAdminAuth();
    if (!updates.email) throw new Error("Email is required");

    const email = updates.email.toLowerCase();
    const password = "12345678";

    return (await this.pb.collection("users").create({
      password,
      passwordConfirm: password,
      ...updates,
      email,
    })) as UserProps;
  }

  async getAdminFollowers(userId: string) {
    await this.ensureAdminAuth();
    return (await this.pb.collection("users").getFullList({
      filter: `admin = "${userId}"`,
      sort: "-created",
      requestKey: null,
    })) as UserProps[];
  }

  async approveUser(userId: string) {
    await this.ensureAdminAuth();
    return (await this.pb
      .collection("users")
      .update(userId, { approved: true })) as UserProps;
  }

  async rejectUser(userId: string) {
    await this.ensureAdminAuth();
    return (await this.pb
      .collection("users")
      .update(userId, { admin: null })) as UserProps;
  }

  async deleteUser(userId: string) {
    await this.ensureAdminAuth();
    await this.pb.collection("users").delete(userId);
  }

  async getUserById(userId: string) {
    await this.ensureAdminAuth();
    const userRecord = await this.pb.collection("users").getFullList({
      filter: `id = "${userId}"`,
      expand: "admin,wallet",
    });
    return userRecord[0] as UserProps;
  }

  // Wallet management
  async checkIsWalletExist({
    address,
    network,
    blacklisted = false,
  }: {
    address: string;
    network: string;
    blacklisted?: boolean;
  }) {
    await this.ensureAdminAuth();
    let filter = `address~"${address.trim()}" && network~"${network.trim()}"`;
    if (blacklisted) filter += ` && blacklisted=${blacklisted}`;
    const result = await this.pb.collection("wallets").getFullList({ filter });
    return result[0];
  }

  async createWallet(userId: string, wallet: WalletType) {
    await this.ensureAdminAuth();
    const newWallet = await this.pb.collection("wallets").create({
      address: wallet.address.trim(),
      network: wallet.network.trim(),
    });

    await this.pb.collection("users").update(userId, {
      wallet: newWallet.id,
    });

    return newWallet;
  }

  // Investment handling
  async handleBeneficiaries(investmentId: string, beneficiariesData: any[]) {
    await this.ensureAdminAuth();
    const existing = await this.pb
      .collection("investment_beneficiaries")
      .getFullList({ filter: `investment="${investmentId}"` });

    const existingMap = new Map(existing.map((b) => [b.user, b]));
    const batch = this.pb.createBatch();

    for (const b of beneficiariesData) {
      const payload = {
        amount: b.amount,
        percentage: b.percentage,
        investment: investmentId,
        user: b.beneficiaryId,
      };

      const existingBeneficiary = existingMap.get(b.beneficiaryId);
      if (existingBeneficiary) {
        batch
          .collection("investment_beneficiaries")
          .update(existingBeneficiary.id, payload);
        existingMap.delete(b.beneficiaryId);
      } else {
        batch.collection("investment_beneficiaries").create(payload);
      }
    }

    for (const old of existingMap.values()) {
      batch.collection("investment_beneficiaries").delete(old.id);
    }

    return await batch.send();
  }

  async createInvestment(adminId: string, data: any) {
    await this.ensureAdminAuth();
    const investmentPayload = {
      user: adminId,
      status: "initialize",
      name: data.name,
      interest_rate: 10,
      duration: data.duration,
    };

    const investment = await this.pb
      .collection("investments")
      .create(investmentPayload);
    await this.handleBeneficiaries(investment.id, data.beneficiaries);
    return investment;
  }

  async updateInvestment(investmentId: string, data: any) {
    await this.ensureAdminAuth();
    const investmentPayload: any = {};
    if (data.name) investmentPayload.name = data.name;
    if (data.duration) investmentPayload.duration = data.duration;

    const investment = await this.pb
      .collection("investments")
      .update(investmentId, investmentPayload);

    if (data.beneficiaries) {
      await this.handleBeneficiaries(investmentId, data.beneficiaries);
    }

    return investment;
  }

  async getInvestments(adminId: string) {
    await this.ensureAdminAuth();
    return await this.pb.collection("investments_view").getFullList({
      filter: `user = "${adminId}"`,
      sort: "-created",
      requestKey: null,
    });
  }

  async getInvestmentBeneficiaries(adminId: string) {
    await this.ensureAdminAuth();
    return await this.pb
      .collection("investment_beneficiaries")
      .getFullList({
        filter: `investment.user='${adminId}'`,
        expand: "user",
        requestKey: null,
      });
  }

  async getMergedInvestments(adminId: string) {
    await this.ensureAdminAuth();
    const [investments, beneficiaries] = await Promise.all([
      this.getInvestments(adminId),
      this.getInvestmentBeneficiaries(adminId),
    ]);

    const beneficiariesByInvestment: Record<string, any[]> = {};
    for (const b of beneficiaries) {
      const invId = b.investment;
      if (!beneficiariesByInvestment[invId]) {
        beneficiariesByInvestment[invId] = [];
      }
      beneficiariesByInvestment[invId].push(b);
    }

    return investments.map((inv) => {
      const bList = beneficiariesByInvestment[inv.id] || [];
      const circleMembers = bList
        .filter((b) => b.user !== adminId)
        .map((b) => b.user);
      const multiSetupData = bList.map((b) => ({
        userId: b.user,
        amount: b.amount,
        percentage: b.percentage,
      }));

      return {
        id: inv.id,
        name: inv.name,
        currentAmount: inv.initial_amount + inv.total_profit,
        duration: inv.duration,
        interestRate: inv.interest_rate,
        startDate: inv.start_date,
        status: inv.status,
        createdAt: inv.created,
        updateAt: inv.updated,
        verifyingPayment: Boolean(inv.withdrawal_request),
        paymentsCount: inv.profit_payments_count,
        withdrawn_at: inv.withdrawn_at,
        circleMembers,
        multiSetupData,
      } as Investment;
    });
  }

  async createWithdrawalRequest(investmentId: string, walletId: string) {
    await this.ensureAdminAuth();
    return await this.pb.collection("withdrawal_requests").create({
      investment: investmentId,
      wallet: walletId,
    });
  }

  async getInvestmentById(investmentId: string) {
    await this.ensureAdminAuth();
    const investments = await this.pb
      .collection("investments_view")
      .getFullList({
        filter: `id = "${investmentId}"`,
      });
    return investments[0];
  }

  async getInvestmentsWithWithdrawalRequest(status?: InvestmentStatusType) {
    await this.ensureAdminAuth();
    const withdrawal_request_filter = `withdrawal_request !=null`;
    const filter = status
      ? `status="${status}" && ${withdrawal_request_filter}`
      : withdrawal_request_filter;

    return await this.pb.collection("investments_view").getFullList({
      filter,
      expand: "investment",
    });
  }

  async triggerSuperUserActions({
    investmentId,
    action,
  }: {
    investmentId: string;
    action: "activate" | "close" | "reject";
  }) {
    await this.ensureAdminAuth();
    const investment = await this.getInvestmentById(investmentId);
    if (!investment) throw new Error("Investment not found");

    const batch = this.pb.createBatch();
    if (
      action === "activate" &&
      investment.status === InvestmentStatus.INITIALIZE &&
      investment.withdrawal_request
    ) {
      batch.collection("investments").update(investment.id, {
        status: InvestmentStatus.ACTIVE,
        start_date: new Date().toISOString(),
      });
    }

    if (
      action === "close" &&
      investment.status === InvestmentStatus.MATURED &&
      investment.withdrawal_request
    ) {
      batch.collection("investments").update(investment.id, {
        status: InvestmentStatus.CLOSED,
        withdrawn_at: new Date().toISOString(),
      });
    }

    if (investment.withdrawal_request) {
      batch.collection("withdrawal_requests").delete(investment.withdrawal_request);
      return await batch.send();
    }

    return null;
  }
}

// ---- Singleton setup ----
let dbServiceInstance: DBService | null = null;

async function initPocketBaseClient({
  host,
  email,
  password,
}: {
  host: string;
  email: string;
  password: string;
}) {
  const pb = new PocketBase(host);
  await pb.admins.authWithPassword(email, password);
  return pb;
}

export async function getDBService(): Promise<DBService> {
  console.log("PocketBase ENV:", {
    host: process.env.NEW_POCKETBASE_HOST,
    email: process.env.NEW_POCKETBASE_EMAIL,
    password: process.env.NEW_POCKETBASE_PASSWORD ? "••••••••" : "undefined",
  });

  if (!dbServiceInstance) {
    const pb = await initPocketBaseClient({
      host: process.env.NEW_POCKETBASE_HOST!,
      email: process.env.NEW_POCKETBASE_EMAIL!,
      password: process.env.NEW_POCKETBASE_PASSWORD!,
    });
    dbServiceInstance = new DBService(pb);
  } else {
    await dbServiceInstance.ensureAdminAuth();
  }

  return dbServiceInstance;
}

export function resetDBService(): void {
  dbServiceInstance = null;
}

export async function getIDBService(): Promise<DBService> {
  const pb = await initPocketBaseClient({
    host: process.env.NEW_POCKETBASE_HOST!,
    email: process.env.NEW_POCKETBASE_EMAIL!,
    password: process.env.NEW_POCKETBASE_PASSWORD!,
  });
  return new DBService(pb);
}
