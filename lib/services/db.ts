import {
  Investment,
  InvestmentStatus,
  InvestmentStatusType,
} from "@/app/data/investmentModel";
import { WalletType } from "@/contexts/admin-provider";
import PocketBase from "pocketbase";
import { determineMaxAge, validateAndDecodeToken } from "./helpers";

type AuthResultProps = {
  record: {
    admin: string;
    approved: boolean;
    avatar: string;
    collectionId: string;
    collectionName: string;
    created: string; // ISO timestamp
    email: string;
    emailVisibility: boolean;
    id: string;
    invite_code: string;
    name: string;
    role: string;
    updated: string; // ISO timestamp
    verified: boolean;
  };
  token: string;
};

type VerifyEmailResponse =
  | {
      expiringTime: number;
      authToken: string;
      success: true;
    }
  | {
      otpId: string;
    }
  | null;

export class DBService {
  public pb: PocketBase;

  constructor(pb: PocketBase) {
    this.pb = pb;
  }

  // Authentication and Token handling

  async requestOtp(email: string) {
    const result: VerifyEmailResponse = await this.pb
      .collection("users")
      .requestOTP(email);
    return result;
  }

  async validateOtp({
    email,
    otpId,
    otp,
  }: {
    email: string;
    otpId: string;
    otp: string;
  }) {
    const result: AuthResultProps = await this.pb
      .collection("users")
      .authWithOTP(otpId, otp);

    if (result.record.email === email) {
      const maxAge = determineMaxAge(result.token);
      if (maxAge === null) {
        return null;
      }
      return {
        expiringTime: maxAge,
        authToken: result.token,
        success: true,
      };
    }
    return null;
  }

  //User management

  async getUserFromToken(token: string): Promise<UserProps | null> {
    const tokenDetails = validateAndDecodeToken(token);
    if (!tokenDetails) {
      return null;
    }

    const userRecords = await this.pb.collection("users").getFullList({
      filter: `id = "${tokenDetails.id}"`,
      expand: "admin,wallet",
    });

    const userRecord = userRecords[0];

    // Parse settings if it exists and is a string
    if (
      userRecord &&
      userRecord.settings &&
      typeof userRecord.settings === "string"
    ) {
      try {
        userRecord.settings = JSON.parse(userRecord.settings);
      } catch (error) {
        console.warn("Failed to parse user settings:", error);
        userRecord.settings = {};
      }
    }

    return (userRecord || null) as UserProps | null;
  }

  // get the super_user from the token
  async getSuperUserFromToken(token: string) {
    const tokenDetails = validateAndDecodeToken(token);
    if (!tokenDetails) {
      return null;
    }
    // get if the id exists in the super_user collection
    const superUsers = await this.pb.collection("_superusers").getFullList({
      filter: `id = "${tokenDetails.id}"`,
    });
    if (superUsers.length === 0) {
      return null;
    }
    return superUsers[0];
  }

  async getInviteCodeOwner(inviteCode: string) {
    const userRecord = await this.pb.collection("users").getFullList({
      filter: `invite_code = "${inviteCode}" && role="admin"`,
    });
    return (userRecord[0] || null) as UserProps | null;
  }

  async updateUserRecord({
    userId,
    updates,
  }: {
    userId: string;
    updates: Partial<UserProps>;
  }) {
    const updatedUser = await this.pb
      .collection("users")
      .update(userId, updates);

    return updatedUser as UserProps;
  }

  async getUserByEmail(email: string, create?: boolean) {
    const lowerEmail = email.toLowerCase().trim();
    const userRecord = await this.pb.collection("users").getFullList({
      filter: `email ~ "${lowerEmail}"`,
    });
    const result = (userRecord[0] || null) as UserProps | null;

    // Check if there is no user and create is true
    if (!result && create) {
      await this.createUserRecord({ email: lowerEmail });
    }

    return result;
  }

  async createUserRecord(updates: Partial<UserProps> & { email: string }) {
    if (!updates.email) {
      throw new Error("Email is required to create a user");
    }

    const email = updates.email.toLowerCase(); // ensure email is lowercase
    const password = "12345678";

    const newUserRecord = await this.pb.collection("users").create({
      password,
      passwordConfirm: password,
      ...updates,
      email, // overwrite with lowercase email
    });

    return newUserRecord as UserProps;
  }

  async getAdminFollowers(userId: string) {
    const users = await this.pb.collection("users").getFullList({
      filter: `admin = "${userId}"`,
      sort: "-created",
      requestKey: null,
    });
    return users as UserProps[];
  }

  async approveUser(userId: string) {
    const updatedUser = await this.pb
      .collection("users")
      .update(userId, { approved: true });
    return updatedUser as UserProps;
  }

  async rejectUser(userId: string) {
    const updatedUser = await this.pb
      .collection("users")
      .update(userId, { admin: null });
    return updatedUser as UserProps;
  }

  async deleteUser(userId: string) {
    await this.pb.collection("users").delete(userId);
  }

  async getUserById(userId: string) {
    const userRecord = await this.pb.collection("users").getFullList({
      filter: `id = "${userId}"`,
      expand: "admin,wallet",
    });
    return userRecord[0] as UserProps;
  }

  //Wallet Management

  async checkIsWalletExist({
    address,
    network,
    blacklisted = false,
  }: {
    address: string;
    network: string;
    blacklisted?: boolean;
  }) {
    let filter = `address~"${address.trim()}" && network~"${network.trim()}"`;
    if (blacklisted) {
      filter += ` && blacklisted=${blacklisted}`;
    }
    const result = await this.pb.collection("wallets").getFullList({
      filter,
    });
    return result[0];
  }

  async createWallet(userId: string, wallet: WalletType) {
    const newWallet = await this.pb.collection("wallets").create({
      address: wallet.address.trim(),
      network: wallet.network.trim(),
    });

    await this.pb.collection("users").update(userId, {
      wallet: newWallet.id,
    });

    return newWallet;
  }

  // Investment & Beneficiaries

  async handleBeneficiaries(investmentId: string, beneficiariesData: any[]) {
    const existing = await this.pb
      .collection("investment_beneficiaries")
      .getFullList({ filter: `investment="${investmentId}"` });

    const existingMap = new Map(existing.map((b) => [b.user, b]));

    const batch = this.pb.createBatch();

    for (const b of beneficiariesData) {
      const payload = {
        amount: b.amount,
        percentage: b.percentage,
        investment: investmentId,
        user: b.beneficiaryId,
      };

      const existingBeneficiary = existingMap.get(b.beneficiaryId);
      if (existingBeneficiary) {
        batch
          .collection("investment_beneficiaries")
          .update(existingBeneficiary.id, payload);
        existingMap.delete(b.beneficiaryId);
      } else {
        batch.collection("investment_beneficiaries").create(payload);
      }
    }
    // delete the leftovers
    for (const old of existingMap.values()) {
      batch.collection("investment_beneficiaries").delete(old.id);
    }
    // send all at once
    const result = await batch.send();

    return result;
  }

  // Create a new investment
  async createInvestment(adminId: string, data: any) {
    const investmentPayload = {
      user: adminId,
      status: "initialize",
      name: data.name,
      interest_rate: 10,
      duration: data.duration,
    };

    const investment = await this.pb
      .collection("investments")
      .create(investmentPayload);
    await this.handleBeneficiaries(investment.id, data.beneficiaries);

    return investment;
  }

  // Update an existing investment
  async updateInvestment(investmentId: string, data: any) {
    const investmentPayload: any = {};

    // Only update fields allowed to change
    if (data.name) investmentPayload.name = data.name;
    if (data.duration) investmentPayload.duration = data.duration;

    const investment = await this.pb
      .collection("investments")
      .update(investmentId, investmentPayload);

    if (data.beneficiaries) {
      await this.handleBeneficiaries(investmentId, data.beneficiaries);
    }

    return investment;
  }

  // list of investment for the user Admin
  async getInvestments(adminId: string) {
    const investments = await this.pb
      .collection("investments_view")
      .getFullList({
        filter: `user = "${adminId}"`,
        sort: "-created",
        requestKey: null,
      });

    return investments;
  }

  async getInvestmentBeneficiaries(adminId: string) {
    const beneficiaries = await this.pb
      .collection("investment_beneficiaries")
      .getFullList({
        filter: `investment.user='${adminId}'`,
        expand: "user",
        requestKey: null,
      });
    return beneficiaries;
  }

  // merge the investment with their beneficiaries
  async getMergedInvestments(adminId: string) {
    // fetch both in parallel
    const [investments, beneficiaries] = await Promise.all([
      this.getInvestments(adminId),
      this.getInvestmentBeneficiaries(adminId),
    ]);

    // group beneficiaries by investment_id
    const beneficiariesByInvestment: Record<string, any[]> = {};
    for (const b of beneficiaries) {
      const invId = b.investment; // assuming this field exists
      if (!beneficiariesByInvestment[invId]) {
        beneficiariesByInvestment[invId] = [];
      }
      beneficiariesByInvestment[invId].push(b);
    }

    // attach beneficiaries to their investment
    const merged = investments.map((inv) => {
      const beneficiaries = beneficiariesByInvestment[inv.id] || [];
      const circleMembers = beneficiaries
        .filter((b) => b.user !== adminId)
        .map((b) => b.user);
      const multiSetupData = beneficiaries.map((b) => {
        return {
          userId: b.user,
          amount: b.amount,
          percentage: b.percentage,
        };
      });
      return {
        id: inv.id,
        name: inv.name,
        currentAmount: inv.initial_amount + inv.total_profit,
        duration: inv.duration,
        interestRate: inv.interest_rate,
        startDate: inv.start_date,
        status: inv.status,
        createdAt: inv.created,
        updateAt: inv.updated,
        verifyingPayment: Boolean(inv.withdrawal_request),
        paymentsCount: inv.profit_payments_count,
        withdrawn_at: inv.withdrawn_at,
        circleMembers,
        multiSetupData,
      } as unknown as Investment;
    });
    return merged;
  }

  // create a withdrawal request for an investment
  async createWithdrawalRequest(investmentId: string, walletId: string) {
    const newRequest = await this.pb.collection("withdrawal_requests").create({
      investment: investmentId,
      wallet: walletId,
    });
    return newRequest;
  }

  // Super User Api functions

  // get the details of an investment when the investmentId is passed
  async getInvestmentById(investmentId: string) {
    const investments = await this.pb
      .collection("investments_view")
      .getFullList({
        filter: `id = "${investmentId}"`,
      });
    return investments[0];
  }

  //get all investment that the status is initialize and have a withdrawal_request
  async getInvestmentsWithWithdrawalRequest(status?: InvestmentStatusType) {
    const withdrawal_request_filter = `withdrawal_request !=null`;

    let filter = `${withdrawal_request_filter}`;
    filter = status
      ? `status="${status}" && ${withdrawal_request_filter}`
      : withdrawal_request_filter;

    const investments = await this.pb
      .collection("investments_view")
      .getFullList({
        filter,
        expand: "investment",
      });
    return investments;
  }

  async triggerSuperUserActions({
    investmentId,
    action,
  }: {
    investmentId: string;
    action: "activate" | "close" | "reject";
  }) {
    const investment = await this.getInvestmentById(investmentId);
    if (!investment) {
      throw new Error("Investment not found");
    }

    const batch = this.pb.createBatch();
    if (action === "activate") {
      if (
        investment &&
        investment.status === InvestmentStatus.INITIALIZE &&
        investment.withdrawal_request
      ) {
        batch.collection("investments").update(investment.id, {
          status: InvestmentStatus.ACTIVE,
          start_date: new Date().toISOString(),
        });
      }
    }

    if (action === "close") {
      if (
        investment &&
        investment.status === InvestmentStatus.MATURED &&
        investment.withdrawal_request
      ) {
        batch.collection("investments").update(investment.id, {
          status: InvestmentStatus.CLOSED,
          withdrawn_at: new Date().toISOString(),
        });
      }
    }
    if (investment.withdrawal_request) {
      batch
        .collection("withdrawal_requests")
        .delete(investment.withdrawal_request);
      return await batch.send();
    }
    return null;
  }





















  
}

// Singleton instance storage
let dbServiceInstance: DBService | null = null;

async function initPocketBaseClient(proxy_credentials: {
  host: string;
  email: string;
  password: string;
  auto_cancellation?: boolean;
}) {
  const pb = new PocketBase(proxy_credentials.host);
  await pb
    .collection("_superusers")
    .authWithPassword(proxy_credentials.email, proxy_credentials.password);

  // Add masking for logging/serialization to the PocketBase instance itself
  const customInspectSymbol = Symbol.for("nodejs.util.inspect.custom");

  // Custom inspect for console.log (Node.js)
  if (typeof pb === "object" && pb !== null) {
    (pb as any)[customInspectSymbol] = function () {
      // Avoid logging authStore directly
      return `PocketBase { authStore: [Masked], ... }`;
    };

    // Custom toJSON for JSON.stringify
    (pb as any).toJSON = function () {
      // Return a safe representation, excluding potentially sensitive parts like authStore
      return {
        _instanceType: "PocketBase",
        _status: "Sensitive Data Masked",
        // You could selectively include non-sensitive properties if needed
        // e.g., baseUrl: this.baseUrl
      };
    };
  }

  return pb;
}

/**
 * Get or create a singleton DBService instance
 * @returns Promise<DBService> - The singleton DBService instance
 */
export async function getDBService(): Promise<DBService> {
  console.log("PocketBase ENV:", {
  host: process.env.NEW_POCKETBASE_HOST,
  email: process.env.NEW_POCKETBASE_EMAIL,
  password: process.env.NEW_POCKETBASE_PASSWORD ? "••••••••" : "undefined",
});

  if (!dbServiceInstance) {
    const pb = await initPocketBaseClient({
      host: process.env.NEW_POCKETBASE_HOST!,
      email: process.env.NEW_POCKETBASE_EMAIL!,
      password: process.env.NEW_POCKETBASE_PASSWORD!,
    });
    dbServiceInstance = new DBService(pb);
  }
  return dbServiceInstance;
}

/**
 * Reset the singleton instance (useful for testing or reconnection)
 */
export function resetDBService(): void {
  dbServiceInstance = null;
}

export async function getIDBService(): Promise<DBService> {
  const pb = await initPocketBaseClient({
    host: process.env.NEW_POCKETBASE_HOST!,
    email: process.env.NEW_POCKETBASE_EMAIL!,
    password: process.env.NEW_POCKETBASE_PASSWORD!,
  });
  return new DBService(pb);
}
