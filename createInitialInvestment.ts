import { getDBService } from "./lib/services/db";

const dbService = await getDBService();

interface Investment {
  id: string;
  status: string;
  wallet: string;
  amount: number;
  profit?: number;
  user: string;
  [key: string]: any;
}

interface WithdrawalRequest {
  investment: string;
  wallet: string;
  amount: number;
  status: "pending" | "approved" | "rejected";
  user: string;
  type?: "investment_completion" | "manual";
}

/**
 * Process investments: Change status from 'active' to 'initialize'
 * and create withdrawal requests
 */
async function processInvestments() {
  const investment = await dbService.pb
    .collection("investments")
    .update("u6f9unn6p768ecz", { status: "initialize", start_date: "" });
  console.log("created", investment);

  const request = await dbService.createWithdrawalRequest(
    "u6f9unn6p768ecz",
    "1ccvj6a5ba4twow"
  );
  console.log(`  ✓ Created withdrawal request: ${request.id}`);
}

// Export functions
export { processInvestments };

// Run if executed directly
if (require.main === module) {
  processInvestments()
    .then((result) => {
      console.log("\n✅ Script completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Script failed:", error);
      process.exit(1);
    });
}
